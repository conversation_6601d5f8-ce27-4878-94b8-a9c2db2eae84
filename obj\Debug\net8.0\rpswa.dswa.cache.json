{"GlobalPropertiesHash": "/rh6Ca+3TXWXLIImbCRvyn8vxAa+x52fuIUCSflPgMg=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["yRhK6MHnuRN1ZiHLDtlaa0TvlCsfbMnvE+w+lOY6fw4=", "3Xiu8cPSgZsraL9Xecu//J5MD9fAgynt+7g/EkihuO8=", "pWkhsBqUaHuv/wUdxG0sBb86ZzTh751vXY5PC7n+8PU=", "WocBwek/n3kvEazbwcT6K4fU69pFqdWAa6LOW3wUqms=", "xladoseiTu8fHJ61ponrBI65Rgoo9uwT0O5cbvNT9sA=", "/bej8t5JBVqSJ521JH5BPuflR+YJFdx5o0L1/+QjMdk=", "gSAAkyYYyILmPDolDgX3OOun8Fbh+0jXiPDGRUxXvkk=", "r9qjAmlDbpIJ+U//ZDDSZ1dTb7MkfFuBmP8++GDo41A=", "1tBUMFIdQAPe6HwNKG4/2MWx7jIZq9cmGVoDVSiYpMU=", "NmCfnc+jFY3b49ZpBV0O1flavPhJJogNcQukqbiaV9E=", "pbnBtvc5ESW9rSh8i0d+GfmusSMTLGYYGFARC3YXPMk=", "idVZegktkem9UmA0Z2Sv0ttzer3CxUW1ufd3vtUifdE=", "TdRWNoIaCqVVcIAEpf9/rDIKipl27JhFWH9r6oNEobc=", "0F4cJO0oNb59v2kfyKSQvXu3ZSyv9dmnOFyzaWu/Ezg=", "YscHBRLN+O34B3fiXT/khQcCizVfH5cMmh2oj+vp690=", "9kAFx1uIHpzAINg+gRIaI8EIE2WoUdTaigZsI8KxrIo=", "WnyGGztsF1c5caDj2YgbVYFthX29CVFGahmmyWnKIKw=", "klloiTEjpah2wuzu33vgUTYtO6wdm+/okYLRmkXadQc=", "rFN4RP15zM7ekfg4tj8NNpjXkhrPqi/Zi4K3kOhVVvs=", "jLZW3TNxr3eIHcdWrMKnOA930IhowrnpgDSKP1aME70=", "mkVHgvUaNNFx5/eAR5l8j5zY67VK1f5cMHqZljBa2fk=", "KYwtiw//iPrYOIq6anEOJG73hrsm/LsuO9DGMahDwIo=", "97R8IiNMPd0aCDnxiQsw1LWwOuTOeNfyT65KLjcrPSI=", "/9qIS64MOrhkh6O/Ely0eBR9NGfOMmybAz3OvCznQvc=", "5uTiBUO1a7UvUyxWNx4y6XQYG44D9E8QuF3WsehYtJ8=", "XUrRZ4ze5w2xoYG43WwX1C/uE3gDaAFZEjtCazj1b18=", "2lp9wlqN6UqyX74SFMP73AzoAPr216hvi4CWm4lPmLI=", "F2FuTlp8SpbbRYT/tt0LBQf1bzd9k+PN/8SYQHstifQ=", "l0860pk5tGxQMN3wMQCfpmYoU9c+5LiPwJPVz0vr03E=", "EIJOcCybWaNZlE5Ad7U69bDc1DAv4agsmXJxJZGTQ2M=", "nOThBp0RLCXAN3iq+kK67dOPx259cK7hl+rEaMJFwuk=", "G0M+Jbb6duRBv+a0EJN9WggxW2UUYJ2DWkG32ZoQCJ4=", "GFKzslptwh1a/wi/hPVL7rNXpvd6V0Xrh/Zk00KG/hE=", "Qt5BV8NDRcHaknvkRbhOIsZgrVQp92WHO5bIKzFImTk=", "i1GU/vflsZUNBBz+5RscKF6BmovfxtLIredEBrFD+Js=", "RPLzbSHG1QDO9ldZ+rkBlNpvo7bGI2ZEMM3nhe59DuU=", "36sHcW3rR6llUdBdSVsZycByYwpXD6B8D/KRGWIj2Vc=", "50IY49F97J5auGE3tPqHft7bUA5ll3SGhdoqwbDMuiE=", "OVa7lFhpuUJ1qqRE5CjexqpEyC3I+shUUR/748CqqU4=", "NLkAkjJBMpH/lT9drPuJIxe6U431E5xVIQ5YyaGjaag=", "z/DFUSJF8IX6JxweJWOTty1i3hPyhZZXTkuZKjEok2U=", "FjpEu601mguLtQ2o6yAHxpHG/oHVat8uFnEqFE6MNBo=", "H+flYs2ascu0ISKf9OaJljnW/7tQ7j2A0sX36/ES3vk=", "6nmP3mAz8B7FE7RVVMEznh7mJiHO6/Qlq9870LKhAtw=", "t43hdW+XXtjKByN8ykZP6hGwXemoWHdPb5Y834a2+DE=", "mc6lAKrlB6cWrKcF+D4lf/vDh3gDa3EeF9kJOzPTPaw=", "XZe9V5HQHqt2iVI1Ka0/lMuNyQhNPVJOLjkxos8l4HE=", "VbVHWoi5ub1s/X1YGRWBKAsxeSsgBJZknN+s9mPZEuA=", "GKTsgDXZj1Imj4I+ogYN1npl96RulypvEQY+bOY4yHI=", "fkTwP8yJs9kcRUbSvr7Jt7xxjn7z8pznmBaUvZxPOCA=", "/tRxEn3fPnuRAQYt1YNAPLDR+sh5J+biOr6JeSLKlO0=", "dMDfRzgJHq7Eq4EjpO4051XFq8nyAT2A/Z8Rrz2sc+M=", "l/WaSSaZ9EZpdydvWk+YB6FyIQSxnSe+lMZjUKiIWJ0=", "fgK+TrQzZe9+omjYbzc4OrgNWzkaga9S/DXnFCHgKQw=", "rlDWdMMAtC8j238BGpc+3a4l8oFG6NfWVaGMj+tuK1I=", "EqvoBWR2MhNm3v1yeZBpKaIb9yM4PBWT/bUzKHEiiuI=", "EL80vqO+QyRyhym4DZCaH6sHy6xC22nxjC+azXHV0c0=", "DnNV8+mh7/+SAWZw9R/XcdM7spaMRnHjsA50iCBAgkE=", "znOmfZWi/VsvlEA+pptQyfd1iMg8Yxje70tMl1ybt8s=", "MBwVyEKDx3y+dQfvosBeSjCXjmgXyhrzsq0+DZ7gqYk=", "wRrd86A2HK/D4KzVGQZdjWAJygAcjbkFW1KWhkrkA94=", "apPXXjv6z4TteaInxArhhn2XbZGCbib+OGPw0djs3bE=", "ooiDKZm19mJxGF2qJpguNKf7TS+W+qX3PwfmN3Jl+Yc=", "hdb8xBhCmPsdB8wxzJI1Qyx1orf181f8E4k0GOwMHyo=", "1f02T16iM/Edigje72O0wKahQxG7nR+uJZ+Mn9kcJPU=", "j4VRHUvvaXNqCYLv6XtgYltNPhoiHDJ4K7WdPzrnGlY=", "Uwv2V2EhWScXUc87BYtTgE8why1rHKjhyafgXsBFvnY=", "HnXhx4ZGm/pcPQAYjBqlE3gb+8DOMHQxj/FBS0JSUKA=", "gEaFN2TmF58cADidW1kaIgfR7Wm0u2nOwG7EsMgIeAE=", "RRFtizw4tIzpxn9WepXN/44fwD63T3XmgC9n6nDhtZk="], "CachedAssets": {"yRhK6MHnuRN1ZiHLDtlaa0TvlCsfbMnvE+w+lOY6fw4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\css\\site.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-09-26T13:46:13.2939863+00:00"}, "3Xiu8cPSgZsraL9Xecu//J5MD9fAgynt+7g/EkihuO8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\favicon.ico", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-09-26T13:46:13.4811696+00:00"}, "pWkhsBqUaHuv/wUdxG0sBb86ZzTh751vXY5PC7n+8PU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\js\\site.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-09-26T13:46:13.2949868+00:00"}, "WocBwek/n3kvEazbwcT6K4fU69pFqdWAa6LOW3wUqms=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-09-26T13:46:13.4329058+00:00"}, "xladoseiTu8fHJ61ponrBI65Rgoo9uwT0O5cbvNT9sA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-09-26T13:46:13.4333171+00:00"}, "/bej8t5JBVqSJ521JH5BPuflR+YJFdx5o0L1/+QjMdk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-09-26T13:46:13.4343784+00:00"}, "gSAAkyYYyILmPDolDgX3OOun8Fbh+0jXiPDGRUxXvkk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-09-26T13:46:13.4343784+00:00"}, "r9qjAmlDbpIJ+U//ZDDSZ1dTb7MkfFuBmP8++GDo41A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-09-26T13:46:13.4356646+00:00"}, "1tBUMFIdQAPe6HwNKG4/2MWx7jIZq9cmGVoDVSiYpMU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-09-26T13:46:13.4356646+00:00"}, "NmCfnc+jFY3b49ZpBV0O1flavPhJJogNcQukqbiaV9E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-09-26T13:46:13.4367342+00:00"}, "pbnBtvc5ESW9rSh8i0d+GfmusSMTLGYYGFARC3YXPMk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-09-26T13:46:13.4367342+00:00"}, "idVZegktkem9UmA0Z2Sv0ttzer3CxUW1ufd3vtUifdE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-09-26T13:46:13.4379507+00:00"}, "TdRWNoIaCqVVcIAEpf9/rDIKipl27JhFWH9r6oNEobc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-09-26T13:46:13.4379507+00:00"}, "0F4cJO0oNb59v2kfyKSQvXu3ZSyv9dmnOFyzaWu/Ezg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-09-26T13:46:13.439116+00:00"}, "YscHBRLN+O34B3fiXT/khQcCizVfH5cMmh2oj+vp690=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-09-26T13:46:13.439116+00:00"}, "9kAFx1uIHpzAINg+gRIaI8EIE2WoUdTaigZsI8KxrIo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-09-26T13:46:13.439116+00:00"}, "WnyGGztsF1c5caDj2YgbVYFthX29CVFGahmmyWnKIKw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-09-26T13:46:13.4403439+00:00"}, "klloiTEjpah2wuzu33vgUTYtO6wdm+/okYLRmkXadQc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-09-26T13:46:13.4413805+00:00"}, "rFN4RP15zM7ekfg4tj8NNpjXkhrPqi/Zi4K3kOhVVvs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-09-26T13:46:13.4413805+00:00"}, "jLZW3TNxr3eIHcdWrMKnOA930IhowrnpgDSKP1aME70=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-09-26T13:46:13.4428978+00:00"}, "mkVHgvUaNNFx5/eAR5l8j5zY67VK1f5cMHqZljBa2fk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-09-26T13:46:13.4439448+00:00"}, "KYwtiw//iPrYOIq6anEOJG73hrsm/LsuO9DGMahDwIo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-09-26T13:46:13.4439448+00:00"}, "97R8IiNMPd0aCDnxiQsw1LWwOuTOeNfyT65KLjcrPSI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-09-26T13:46:13.444944+00:00"}, "/9qIS64MOrhkh6O/Ely0eBR9NGfOMmybAz3OvCznQvc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-09-26T13:46:13.4459486+00:00"}, "5uTiBUO1a7UvUyxWNx4y6XQYG44D9E8QuF3WsehYtJ8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-09-26T13:46:13.4469486+00:00"}, "XUrRZ4ze5w2xoYG43WwX1C/uE3gDaAFZEjtCazj1b18=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-09-26T13:46:13.4480308+00:00"}, "2lp9wlqN6UqyX74SFMP73AzoAPr216hvi4CWm4lPmLI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-09-26T13:46:13.4480308+00:00"}, "F2FuTlp8SpbbRYT/tt0LBQf1bzd9k+PN/8SYQHstifQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-09-26T13:46:13.4491721+00:00"}, "l0860pk5tGxQMN3wMQCfpmYoU9c+5LiPwJPVz0vr03E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-09-26T13:46:13.4514692+00:00"}, "EIJOcCybWaNZlE5Ad7U69bDc1DAv4agsmXJxJZGTQ2M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-09-26T13:46:13.4514692+00:00"}, "nOThBp0RLCXAN3iq+kK67dOPx259cK7hl+rEaMJFwuk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-09-26T13:46:13.4539779+00:00"}, "G0M+Jbb6duRBv+a0EJN9WggxW2UUYJ2DWkG32ZoQCJ4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-09-26T13:46:13.4539779+00:00"}, "GFKzslptwh1a/wi/hPVL7rNXpvd6V0Xrh/Zk00KG/hE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-09-26T13:46:13.4559862+00:00"}, "Qt5BV8NDRcHaknvkRbhOIsZgrVQp92WHO5bIKzFImTk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-09-26T13:46:13.4573596+00:00"}, "i1GU/vflsZUNBBz+5RscKF6BmovfxtLIredEBrFD+Js=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-09-26T13:46:13.4593591+00:00"}, "RPLzbSHG1QDO9ldZ+rkBlNpvo7bGI2ZEMM3nhe59DuU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-09-26T13:46:13.4603597+00:00"}, "36sHcW3rR6llUdBdSVsZycByYwpXD6B8D/KRGWIj2Vc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-09-26T13:46:13.4615618+00:00"}, "50IY49F97J5auGE3tPqHft7bUA5ll3SGhdoqwbDMuiE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-09-26T13:46:13.4630661+00:00"}, "OVa7lFhpuUJ1qqRE5CjexqpEyC3I+shUUR/748CqqU4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-09-26T13:46:13.4640928+00:00"}, "NLkAkjJBMpH/lT9drPuJIxe6U431E5xVIQ5YyaGjaag=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-09-26T13:46:13.4651002+00:00"}, "z/DFUSJF8IX6JxweJWOTty1i3hPyhZZXTkuZKjEok2U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-09-26T13:46:13.4669455+00:00"}, "FjpEu601mguLtQ2o6yAHxpHG/oHVat8uFnEqFE6MNBo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-09-26T13:46:13.4679489+00:00"}, "H+flYs2ascu0ISKf9OaJljnW/7tQ7j2A0sX36/ES3vk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-09-26T13:46:13.4689525+00:00"}, "6nmP3mAz8B7FE7RVVMEznh7mJiHO6/Qlq9870LKhAtw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-09-26T13:46:13.4689525+00:00"}, "t43hdW+XXtjKByN8ykZP6hGwXemoWHdPb5Y834a2+DE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-09-26T13:46:13.470128+00:00"}, "mc6lAKrlB6cWrKcF+D4lf/vDh3gDa3EeF9kJOzPTPaw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-09-26T13:46:13.4713013+00:00"}, "XZe9V5HQHqt2iVI1Ka0/lMuNyQhNPVJOLjkxos8l4HE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-09-26T13:46:13.4713013+00:00"}, "VbVHWoi5ub1s/X1YGRWBKAsxeSsgBJZknN+s9mPZEuA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-09-26T13:46:13.4793722+00:00"}, "GKTsgDXZj1Imj4I+ogYN1npl96RulypvEQY+bOY4yHI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-09-26T13:46:13.4833782+00:00"}, "fkTwP8yJs9kcRUbSvr7Jt7xxjn7z8pznmBaUvZxPOCA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-09-26T13:46:13.4833782+00:00"}, "/tRxEn3fPnuRAQYt1YNAPLDR+sh5J+biOr6JeSLKlO0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-09-26T13:46:13.4845435+00:00"}, "dMDfRzgJHq7Eq4EjpO4051XFq8nyAT2A/Z8Rrz2sc+M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-09-26T13:46:13.2996632+00:00"}, "l/WaSSaZ9EZpdydvWk+YB6FyIQSxnSe+lMZjUKiIWJ0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-09-26T13:46:13.3008366+00:00"}, "fgK+TrQzZe9+omjYbzc4OrgNWzkaga9S/DXnFCHgKQw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-09-26T13:46:13.3008366+00:00"}, "rlDWdMMAtC8j238BGpc+3a4l8oFG6NfWVaGMj+tuK1I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-09-26T13:46:13.3018649+00:00"}, "EqvoBWR2MhNm3v1yeZBpKaIb9yM4PBWT/bUzKHEiiuI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-09-26T13:46:13.4811696+00:00"}, "EL80vqO+QyRyhym4DZCaH6sHy6xC22nxjC+azXHV0c0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-09-26T13:46:13.2972673+00:00"}, "DnNV8+mh7/+SAWZw9R/XcdM7spaMRnHjsA50iCBAgkE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-09-26T13:46:13.2982788+00:00"}, "znOmfZWi/VsvlEA+pptQyfd1iMg8Yxje70tMl1ybt8s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-09-26T13:46:13.2982788+00:00"}, "MBwVyEKDx3y+dQfvosBeSjCXjmgXyhrzsq0+DZ7gqYk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "TravelTripProject", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\TravelTripProject\\TravelTripProject\\wwwroot\\", "BasePath": "_content/TravelTripProject", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-09-26T13:46:13.480665+00:00"}}, "CachedCopyCandidates": {}}