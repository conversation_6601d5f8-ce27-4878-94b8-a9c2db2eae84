using TravelTripProject.Models.DTOs;
using TravelTripProject.Models.ViewModels;

namespace TravelTripProject.Services.Interfaces
{
    public interface IBookingService
    {
        Task<IEnumerable<BookingDto>> GetAllBookingsAsync();
        Task<BookingDto?> GetBookingByIdAsync(int id);
        Task<BookingDto?> GetBookingByNumberAsync(string bookingNumber);
        Task<BookingDetailsViewModel?> GetBookingDetailsAsync(int id);
        Task<IEnumerable<BookingDto>> GetBookingsByUserAsync(int userId);
        Task<IEnumerable<BookingDto>> GetBookingsByStatusAsync(string status);
        Task<IEnumerable<BookingDto>> GetPendingBookingsAsync();
        Task<IEnumerable<BookingDto>> GetRecentBookingsAsync(int count = 10);
        Task<PagedResult<BookingDto>> GetPagedBookingsAsync(int pageNumber, int pageSize, BookingFilterDto? filter = null);
        Task<BookingDto> CreateBookingAsync(CreateBookingDto createDto);
        Task<BookingDto?> UpdateBookingAsync(int id, UpdateBookingDto updateDto);
        Task<bool> CancelBookingAsync(int id, string reason);
        Task<bool> ConfirmBookingAsync(int id);
        Task<bool> CompleteBookingAsync(int id);
        Task<bool> UpdateBookingStatusAsync(int id, string status);
        Task<decimal> CalculateTotalAmountAsync(CreateBookingDto bookingDto);
        Task<bool> IsBookingAvailableAsync(CreateBookingDto bookingDto);
    }
}
