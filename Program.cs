using Microsoft.EntityFrameworkCore;
using TravelTripProject.Data;
using TravelTripProject.Data.Repositories.Interfaces;
using TravelTripProject.Services.Interfaces;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Database Configuration
builder.Services.AddDbContext<TravelTripDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Repository Registration
// builder.Services.AddScoped<IGenericRepository<T>, GenericRepository<T>>();
// builder.Services.AddScoped<IDestinationRepository, DestinationRepository>();
// builder.Services.AddScoped<ITourRepository, TourRepository>();
// builder.Services.AddScoped<IHotelRepository, HotelRepository>();
// builder.Services.AddScoped<IBookingRepository, BookingRepository>();
// builder.Services.AddScoped<IUserRepository, UserRepository>();
// builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

// Service Registration
// builder.Services.AddScoped<IDestinationService, DestinationService>();
// builder.Services.AddScoped<ITourService, TourService>();
// builder.Services.AddScoped<IBookingService, BookingService>();
// builder.Services.AddScoped<IUserService, UserService>();

// AutoMapper Configuration
// builder.Services.AddAutoMapper(typeof(Program));

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
