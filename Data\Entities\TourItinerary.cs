using System.ComponentModel.DataAnnotations;

namespace TravelTripProject.Data.Entities
{
    public class TourItinerary
    {
        public int Id { get; set; }
        
        public int Day { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string Location { get; set; } = string.Empty;
        
        public TimeSpan StartTime { get; set; }
        
        public TimeSpan EndTime { get; set; }
        
        [StringLength(500)]
        public string Activities { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Meals { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string Accommodation { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Foreign Keys
        public int TourId { get; set; }
        
        // Navigation Properties
        public virtual Tour Tour { get; set; } = null!;
    }
}
