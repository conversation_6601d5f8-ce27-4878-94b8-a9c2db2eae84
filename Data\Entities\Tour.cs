using System.ComponentModel.DataAnnotations;

namespace TravelTripProject.Data.Entities
{
    public class Tour
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        public decimal Price { get; set; }
        
        public int Duration { get; set; } // Gün cinsinden
        
        public int MaxCapacity { get; set; }
        
        public int AvailableSpots { get; set; }
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        [StringLength(255)]
        public string ImageUrl { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedDate { get; set; }
        
        // Foreign Keys
        public int DestinationId { get; set; }
        
        // Navigation Properties
        public virtual Destination Destination { get; set; } = null!;
        public virtual ICollection<Booking> Bookings { get; set; } = new List<Booking>();
        public virtual ICollection<TourItinerary> TourItineraries { get; set; } = new List<TourItinerary>();
    }
}
