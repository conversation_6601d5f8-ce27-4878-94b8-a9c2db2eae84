using Microsoft.AspNetCore.Mvc;
using TravelTripProject.Models.ViewModels;
using TravelTripProject.Models.DTOs;

namespace TravelTripProject.Controllers
{
    public class DestinationsController : Controller
    {
        // private readonly IDestinationService _destinationService;
        // private readonly ITourService _tourService;
        // private readonly IHotelService _hotelService;

        // public DestinationsController(
        //     IDestinationService destinationService,
        //     ITourService tourService,
        //     IHotelService hotelService)
        // {
        //     _destinationService = destinationService;
        //     _tourService = tourService;
        //     _hotelService = hotelService;
        // }

        public async Task<IActionResult> Index(int page = 1, string? search = null, string? country = null, decimal? minPrice = null, decimal? maxPrice = null)
        {
            // Geçici olarak mock data kullanıyoruz
            var destinations = GetMockDestinations();
            
            var viewModel = new DestinationListViewModel
            {
                Destinations = new PagedResult<DestinationDto>
                {
                    Items = destinations,
                    TotalCount = destinations.Count(),
                    PageNumber = page,
                    PageSize = 12
                },
                SearchTerm = search,
                SelectedCountry = country,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                Countries = new[] { "Türkiye", "İtalya", "Fransa", "İspanya", "Yunanistan", "İngiltere" }
            };

            return View(viewModel);
        }

        public async Task<IActionResult> Details(int id)
        {
            // Geçici olarak mock data kullanıyoruz
            var destination = GetMockDestinations().FirstOrDefault(d => d.Id == id);
            if (destination == null)
            {
                return NotFound();
            }

            var viewModel = new DestinationDetailsViewModel
            {
                Destination = destination,
                Tours = GetMockTours().Where(t => t.DestinationId == id),
                Hotels = GetMockHotels().Where(h => h.DestinationId == id),
                RelatedDestinations = GetMockDestinations().Where(d => d.Id != id && d.Country == destination.Country).Take(3)
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Search(SearchFormViewModel model)
        {
            // Arama sonuçlarını işle
            return RedirectToAction("Index", new { search = model.Destination });
        }

        // Mock data methods - gerçek implementasyonda silinecek
        private List<DestinationDto> GetMockDestinations()
        {
            return new List<DestinationDto>
            {
                new DestinationDto { Id = 1, Name = "İstanbul", Country = "Türkiye", City = "İstanbul", Price = 1299, Duration = 3, ImageUrl = "https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b", IsActive = true, TourCount = 5, HotelCount = 12 },
                new DestinationDto { Id = 2, Name = "Antalya", Country = "Türkiye", City = "Antalya", Price = 899, Duration = 5, ImageUrl = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4", IsActive = true, TourCount = 8, HotelCount = 15 },
                new DestinationDto { Id = 3, Name = "Kapadokya", Country = "Türkiye", City = "Nevşehir", Price = 1599, Duration = 4, ImageUrl = "https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e", IsActive = true, TourCount = 6, HotelCount = 8 },
                new DestinationDto { Id = 4, Name = "Paris", Country = "Fransa", City = "Paris", Price = 2499, Duration = 6, ImageUrl = "https://images.unsplash.com/photo-1502602898536-47ad22581b52", IsActive = true, TourCount = 10, HotelCount = 20 },
                new DestinationDto { Id = 5, Name = "Roma", Country = "İtalya", City = "Roma", Price = 1899, Duration = 5, ImageUrl = "https://images.unsplash.com/photo-1552832230-c0197dd311b5", IsActive = true, TourCount = 7, HotelCount = 14 },
                new DestinationDto { Id = 6, Name = "Londra", Country = "İngiltere", City = "Londra", Price = 2199, Duration = 4, ImageUrl = "https://images.unsplash.com/photo-1513635269975-59663e0ac1ad", IsActive = true, TourCount = 9, HotelCount = 18 }
            };
        }

        private List<TourDto> GetMockTours()
        {
            return new List<TourDto>
            {
                new TourDto { Id = 1, Name = "İstanbul Tarihi Tur", DestinationId = 1, Price = 299, Duration = 1, MaxCapacity = 20, AvailableSpots = 15, StartDate = DateTime.Now.AddDays(7), EndDate = DateTime.Now.AddDays(8) },
                new TourDto { Id = 2, Name = "Boğaz Turu", DestinationId = 1, Price = 199, Duration = 1, MaxCapacity = 30, AvailableSpots = 25, StartDate = DateTime.Now.AddDays(10), EndDate = DateTime.Now.AddDays(11) }
            };
        }

        private List<HotelDto> GetMockHotels()
        {
            return new List<HotelDto>
            {
                new HotelDto { Id = 1, Name = "Grand Hotel İstanbul", DestinationId = 1, StarRating = 5, PricePerNight = 450, HasWifi = true, HasPool = true, HasSpa = true, AvailableRooms = 10 },
                new HotelDto { Id = 2, Name = "Boutique Hotel Sultanahmet", DestinationId = 1, StarRating = 4, PricePerNight = 280, HasWifi = true, HasPool = false, HasSpa = false, AvailableRooms = 5 }
            };
        }
    }
}
