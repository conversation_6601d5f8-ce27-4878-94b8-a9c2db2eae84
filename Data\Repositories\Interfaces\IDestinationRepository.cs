using TravelTripProject.Data.Entities;

namespace TravelTripProject.Data.Repositories.Interfaces
{
    public interface IDestinationRepository : IGenericRepository<Destination>
    {
        Task<IEnumerable<Destination>> GetPopularDestinationsAsync(int count = 6);
        Task<IEnumerable<Destination>> GetDestinationsByCountryAsync(string country);
        Task<IEnumerable<Destination>> SearchDestinationsAsync(string searchTerm);
        Task<Destination?> GetDestinationWithDetailsAsync(int id);
        Task<IEnumerable<Destination>> GetActiveDestinationsAsync();
        Task<IEnumerable<Destination>> GetDestinationsByPriceRangeAsync(decimal minPrice, decimal maxPrice);
    }
}
