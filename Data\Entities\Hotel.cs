using System.ComponentModel.DataAnnotations;

namespace TravelTripProject.Data.Entities
{
    public class Hotel
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string Address { get; set; } = string.Empty;
        
        public int StarRating { get; set; } // 1-5 yıldız
        
        public decimal PricePerNight { get; set; }
        
        [StringLength(255)]
        public string ImageUrl { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;
        
        public bool HasWifi { get; set; }
        
        public bool HasPool { get; set; }
        
        public bool HasSpa { get; set; }
        
        public bool HasGym { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedDate { get; set; }
        
        // Foreign Keys
        public int DestinationId { get; set; }
        
        // Navigation Properties
        public virtual Destination Destination { get; set; } = null!;
        public virtual ICollection<Booking> Bookings { get; set; } = new List<Booking>();
        public virtual ICollection<Room> Rooms { get; set; } = new List<Room>();
    }
}
