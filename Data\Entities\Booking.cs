using System.ComponentModel.DataAnnotations;

namespace TravelTripProject.Data.Entities
{
    public class Booking
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(20)]
        public string BookingNumber { get; set; } = string.Empty;
        
        public DateTime BookingDate { get; set; } = DateTime.Now;
        
        public DateTime CheckInDate { get; set; }
        
        public DateTime CheckOutDate { get; set; }
        
        public int NumberOfGuests { get; set; }
        
        public decimal TotalAmount { get; set; }
        
        public decimal PaidAmount { get; set; } = 0;
        
        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, Confirmed, Cancelled, Completed
        
        [StringLength(20)]
        public string PaymentStatus { get; set; } = "Pending"; // Pending, Paid, Partial, Refunded
        
        [StringLength(500)]
        public string SpecialRequests { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedDate { get; set; }
        
        // Foreign Keys
        public int UserId { get; set; }
        public int? DestinationId { get; set; }
        public int? TourId { get; set; }
        public int? HotelId { get; set; }
        public int? RoomId { get; set; }
        
        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Destination? Destination { get; set; }
        public virtual Tour? Tour { get; set; }
        public virtual Hotel? Hotel { get; set; }
        public virtual Room? Room { get; set; }
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }
}
