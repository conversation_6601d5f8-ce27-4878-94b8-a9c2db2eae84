using System.ComponentModel.DataAnnotations;

namespace TravelTripProject.Data.Entities
{
    public class Destination
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string Country { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string City { get; set; } = string.Empty;
        
        [StringLength(255)]
        public string ImageUrl { get; set; } = string.Empty;
        
        public decimal Price { get; set; }
        
        public int Duration { get; set; } // Gün cinsinden
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedDate { get; set; }
        
        // Navigation Properties
        public virtual ICollection<Tour> Tours { get; set; } = new List<Tour>();
        public virtual ICollection<Hotel> Hotels { get; set; } = new List<Hotel>();
        public virtual ICollection<Booking> Bookings { get; set; } = new List<Booking>();
    }
}
