using TravelTripProject.Models.DTOs;

namespace TravelTripProject.Models.ViewModels
{
    public class DestinationDetailsViewModel
    {
        public DestinationDto Destination { get; set; } = new();
        public IEnumerable<TourDto> Tours { get; set; } = new List<TourDto>();
        public IEnumerable<HotelDto> Hotels { get; set; } = new List<HotelDto>();
        public IEnumerable<DestinationDto> RelatedDestinations { get; set; } = new List<DestinationDto>();
    }

    public class DestinationListViewModel
    {
        public PagedResult<DestinationDto> Destinations { get; set; } = new();
        public string? SearchTerm { get; set; }
        public string? SelectedCountry { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public IEnumerable<string> Countries { get; set; } = new List<string>();
    }

    public class HomePageViewModel
    {
        public IEnumerable<DestinationDto> PopularDestinations { get; set; } = new List<DestinationDto>();
        public IEnumerable<TourDto> FeaturedTours { get; set; } = new List<TourDto>();
        public IEnumerable<HotelDto> FeaturedHotels { get; set; } = new List<HotelDto>();
        public SearchFormViewModel SearchForm { get; set; } = new();
    }

    public class SearchFormViewModel
    {
        public string? Destination { get; set; }
        public DateTime? CheckInDate { get; set; }
        public DateTime? CheckOutDate { get; set; }
        public int Guests { get; set; } = 2;
        public IEnumerable<DestinationDto> AvailableDestinations { get; set; } = new List<DestinationDto>();
    }

    public class TourDetailsViewModel
    {
        public TourDto Tour { get; set; } = new();
        public DestinationDto? Destination { get; set; }
        public IEnumerable<TourItineraryDto> Itinerary { get; set; } = new List<TourItineraryDto>();
        public IEnumerable<TourDto> RelatedTours { get; set; } = new List<TourDto>();
    }

    public class TourItineraryDto
    {
        public int Day { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
    }
}
