using TravelTripProject.Models.DTOs;

namespace TravelTripProject.Models.ViewModels
{
    public class UserProfileViewModel
    {
        public UserDto User { get; set; } = new();
        public IEnumerable<BookingDto> RecentBookings { get; set; } = new List<BookingDto>();
        public int TotalBookings { get; set; }
        public decimal TotalSpent { get; set; }
        public string MembershipLevel { get; set; } = "Bronze";
    }

    public class LoginViewModel
    {
        public LoginDto Login { get; set; } = new();
        public string? ReturnUrl { get; set; }
        public bool ShowRememberMe { get; set; } = true;
    }

    public class RegisterViewModel
    {
        public RegisterDto Register { get; set; } = new();
        public IEnumerable<string> Countries { get; set; } = new List<string>();
        public IEnumerable<string> GenderOptions { get; set; } = new List<string> { "Erkek", "Kadın", "Diğer" };
    }

    public class ForgotPasswordViewModel
    {
        public string Email { get; set; } = string.Empty;
        public bool EmailSent { get; set; }
    }

    public class ResetPasswordViewModel
    {
        public string Email { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public class ChangePasswordViewModel
    {
        public string CurrentPassword { get; set; } = string.Empty;
        public string NewPassword { get; set; } = string.Empty;
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
