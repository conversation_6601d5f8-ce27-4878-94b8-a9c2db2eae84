using TravelTripProject.Data.Entities;

namespace TravelTripProject.Data.Repositories.Interfaces
{
    public interface IUserRepository : IGenericRepository<User>
    {
        Task<User?> GetByEmailAsync(string email);
        Task<bool> EmailExistsAsync(string email);
        Task<User?> GetUserWithBookingsAsync(int id);
        Task<IEnumerable<User>> GetActiveUsersAsync();
        Task<bool> UpdateLastLoginAsync(int userId);
        Task<bool> ConfirmEmailAsync(int userId);
        Task<bool> UpdatePasswordAsync(int userId, string newPasswordHash);
    }
}
