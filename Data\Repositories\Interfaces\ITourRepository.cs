using TravelTripProject.Data.Entities;

namespace TravelTripProject.Data.Repositories.Interfaces
{
    public interface ITourRepository : IGenericRepository<Tour>
    {
        Task<IEnumerable<Tour>> GetToursByDestinationAsync(int destinationId);
        Task<IEnumerable<Tour>> GetAvailableToursAsync();
        Task<IEnumerable<Tour>> GetToursByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<Tour?> GetTourWithItineraryAsync(int id);
        Task<IEnumerable<Tour>> GetToursByPriceRangeAsync(decimal minPrice, decimal maxPrice);
        Task<IEnumerable<Tour>> GetUpcomingToursAsync();
        Task<bool> UpdateAvailableSpotsAsync(int tourId, int spotsToReduce);
    }
}
