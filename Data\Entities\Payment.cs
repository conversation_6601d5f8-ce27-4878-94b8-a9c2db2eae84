using System.ComponentModel.DataAnnotations;

namespace TravelTripProject.Data.Entities
{
    public class Payment
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string PaymentNumber { get; set; } = string.Empty;
        
        public decimal Amount { get; set; }
        
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        
        [StringLength(20)]
        public string PaymentMethod { get; set; } = string.Empty; // CreditCard, BankTransfer, Cash, etc.
        
        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, Success, Failed, Refunded
        
        [StringLength(100)]
        public string TransactionId { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Foreign Keys
        public int BookingId { get; set; }
        
        // Navigation Properties
        public virtual Booking Booking { get; set; } = null!;
    }
}
