using TravelTripProject.Data.Entities;

namespace TravelTripProject.Data.Repositories.Interfaces
{
    public interface IHotelRepository : IGenericRepository<Hotel>
    {
        Task<IEnumerable<Hotel>> GetHotelsByDestinationAsync(int destinationId);
        Task<IEnumerable<Hotel>> GetHotelsByStarRatingAsync(int starRating);
        Task<IEnumerable<Hotel>> GetHotelsWithAvailableRoomsAsync(DateTime checkIn, DateTime checkOut);
        Task<Hotel?> GetHotelWithRoomsAsync(int id);
        Task<IEnumerable<Hotel>> GetHotelsByPriceRangeAsync(decimal minPrice, decimal maxPrice);
        Task<IEnumerable<Hotel>> SearchHotelsAsync(string searchTerm);
        Task<IEnumerable<Hotel>> GetActiveHotelsAsync();
    }
}
