using TravelTripProject.Models.DTOs;
using TravelTripProject.Models.ViewModels;

namespace TravelTripProject.Services.Interfaces
{
    public interface IDestinationService
    {
        Task<IEnumerable<DestinationDto>> GetAllDestinationsAsync();
        Task<DestinationDto?> GetDestinationByIdAsync(int id);
        Task<DestinationDetailsViewModel?> GetDestinationDetailsAsync(int id);
        Task<IEnumerable<DestinationDto>> GetPopularDestinationsAsync(int count = 6);
        Task<IEnumerable<DestinationDto>> SearchDestinationsAsync(string searchTerm);
        Task<IEnumerable<DestinationDto>> GetDestinationsByCountryAsync(string country);
        Task<IEnumerable<DestinationDto>> GetDestinationsByPriceRangeAsync(decimal minPrice, decimal maxPrice);
        Task<PagedResult<DestinationDto>> GetPagedDestinationsAsync(int pageNumber, int pageSize, string? searchTerm = null);
        Task<DestinationDto> CreateDestinationAsync(CreateDestinationDto createDto);
        Task<DestinationDto?> UpdateDestinationAsync(int id, UpdateDestinationDto updateDto);
        Task<bool> DeleteDestinationAsync(int id);
        Task<bool> ActivateDestinationAsync(int id);
        Task<bool> DeactivateDestinationAsync(int id);
    }
}
