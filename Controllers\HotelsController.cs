using Microsoft.AspNetCore.Mvc;
using TravelTripProject.Models.ViewModels;
using TravelTripProject.Models.DTOs;

namespace TravelTripProject.Controllers
{
    public class HotelsController : Controller
    {
        // private readonly IHotelService _hotelService;
        // private readonly IDestinationService _destinationService;

        // public HotelsController(IHotelService hotelService, IDestinationService destinationService)
        // {
        //     _hotelService = hotelService;
        //     _destinationService = destinationService;
        // }

        public async Task<IActionResult> Index(int page = 1, int? destinationId = null, int? starRating = null, decimal? minPrice = null, decimal? maxPrice = null)
        {
            // Geçici olarak mock data kullanıyoruz
            var hotels = GetMockHotels();
            
            // Filtreleme
            if (destinationId.HasValue)
                hotels = hotels.Where(h => h.DestinationId == destinationId.Value).ToList();
            
            if (starRating.HasValue)
                hotels = hotels.Where(h => h.StarRating == starRating.Value).ToList();
                
            if (minPrice.HasValue)
                hotels = hotels.Where(h => h.PricePerNight >= minPrice.Value).ToList();
                
            if (maxPrice.HasValue)
                hotels = hotels.Where(h => h.PricePerNight <= maxPrice.Value).ToList();

            var viewModel = new HotelListViewModel
            {
                Hotels = new PagedResult<HotelDto>
                {
                    Items = hotels,
                    TotalCount = hotels.Count,
                    PageNumber = page,
                    PageSize = 12
                },
                Filter = new HotelFilterDto
                {
                    DestinationId = destinationId,
                    StarRating = starRating,
                    MinPrice = minPrice,
                    MaxPrice = maxPrice
                },
                Destinations = GetMockDestinations()
            };

            return View(viewModel);
        }

        public async Task<IActionResult> Details(int id)
        {
            // Geçici olarak mock data kullanıyoruz
            var hotel = GetMockHotels().FirstOrDefault(h => h.Id == id);
            if (hotel == null)
            {
                return NotFound();
            }

            var destination = GetMockDestinations().FirstOrDefault(d => d.Id == hotel.DestinationId);

            var viewModel = new HotelDetailsViewModel
            {
                Hotel = hotel,
                Destination = destination,
                Rooms = GetMockRooms().Where(r => r.HotelId == id).ToList(),
                NearbyHotels = GetMockHotels().Where(h => h.Id != id && h.DestinationId == hotel.DestinationId).Take(3).ToList()
            };

            return View(viewModel);
        }

        // Mock data methods
        private List<HotelDto> GetMockHotels()
        {
            return new List<HotelDto>
            {
                new HotelDto 
                { 
                    Id = 1, 
                    Name = "Grand Hotel İstanbul", 
                    Description = "Lüks 5 yıldızlı otel",
                    Address = "Sultanahmet, İstanbul",
                    DestinationId = 1, 
                    DestinationName = "İstanbul",
                    StarRating = 5, 
                    PricePerNight = 450, 
                    HasWifi = true, 
                    HasPool = true, 
                    HasSpa = true, 
                    HasGym = true,
                    AvailableRooms = 10,
                    ImageUrl = "https://images.unsplash.com/photo-1566073771259-6a8506099945",
                    IsActive = true,
                    Phone = "+90 212 123 45 67",
                    Email = "<EMAIL>"
                },
                new HotelDto 
                { 
                    Id = 2, 
                    Name = "Boutique Hotel Sultanahmet", 
                    Description = "Tarihi bölgede butik otel",
                    Address = "Sultanahmet Meydanı, İstanbul",
                    DestinationId = 1, 
                    DestinationName = "İstanbul",
                    StarRating = 4, 
                    PricePerNight = 280, 
                    HasWifi = true, 
                    HasPool = false, 
                    HasSpa = false, 
                    HasGym = false,
                    AvailableRooms = 5,
                    ImageUrl = "https://images.unsplash.com/photo-1551882547-ff40c63fe5fa",
                    IsActive = true,
                    Phone = "+90 212 987 65 43",
                    Email = "<EMAIL>"
                },
                new HotelDto 
                { 
                    Id = 3, 
                    Name = "Antalya Resort & Spa", 
                    Description = "Deniz kenarında tatil köyü",
                    Address = "Lara Plajı, Antalya",
                    DestinationId = 2, 
                    DestinationName = "Antalya",
                    StarRating = 5, 
                    PricePerNight = 380, 
                    HasWifi = true, 
                    HasPool = true, 
                    HasSpa = true, 
                    HasGym = true,
                    AvailableRooms = 15,
                    ImageUrl = "https://images.unsplash.com/photo-1520250497591-112f2f40a3f4",
                    IsActive = true,
                    Phone = "+90 242 123 45 67",
                    Email = "<EMAIL>"
                }
            };
        }

        private List<DestinationDto> GetMockDestinations()
        {
            return new List<DestinationDto>
            {
                new DestinationDto { Id = 1, Name = "İstanbul", Country = "Türkiye" },
                new DestinationDto { Id = 2, Name = "Antalya", Country = "Türkiye" },
                new DestinationDto { Id = 3, Name = "Kapadokya", Country = "Türkiye" }
            };
        }

        private List<RoomDto> GetMockRooms()
        {
            return new List<RoomDto>
            {
                new RoomDto { Id = 1, HotelId = 1, RoomNumber = "101", RoomType = "Deluxe", MaxOccupancy = 2, PricePerNight = 450, Size = 35, HasBalcony = true, HasSeaView = false, IsAvailable = true },
                new RoomDto { Id = 2, HotelId = 1, RoomNumber = "201", RoomType = "Suite", MaxOccupancy = 4, PricePerNight = 650, Size = 55, HasBalcony = true, HasSeaView = true, IsAvailable = true },
                new RoomDto { Id = 3, HotelId = 2, RoomNumber = "301", RoomType = "Standard", MaxOccupancy = 2, PricePerNight = 280, Size = 25, HasBalcony = false, HasSeaView = false, IsAvailable = true }
            };
        }
    }

    // Eksik ViewModel ve DTO'lar
    public class HotelListViewModel
    {
        public PagedResult<HotelDto> Hotels { get; set; } = new();
        public HotelFilterDto Filter { get; set; } = new();
        public IEnumerable<DestinationDto> Destinations { get; set; } = new List<DestinationDto>();
    }

    public class HotelDetailsViewModel
    {
        public HotelDto Hotel { get; set; } = new();
        public DestinationDto? Destination { get; set; }
        public IEnumerable<RoomDto> Rooms { get; set; } = new List<RoomDto>();
        public IEnumerable<HotelDto> NearbyHotels { get; set; } = new List<HotelDto>();
    }

    public class HotelFilterDto
    {
        public int? DestinationId { get; set; }
        public int? StarRating { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public bool? HasWifi { get; set; }
        public bool? HasPool { get; set; }
        public bool? HasSpa { get; set; }
        public bool? HasGym { get; set; }
    }
}
