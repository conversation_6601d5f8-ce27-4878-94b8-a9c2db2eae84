using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using TravelTripProject.Models;
using TravelTripProject.Models.ViewModels;
using TravelTripProject.Models.DTOs;

namespace TravelTripProject.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        public IActionResult Index()
        {
            // Geçici olarak mock data kullanıyoruz
            var viewModel = new HomePageViewModel
            {
                PopularDestinations = GetMockDestinations().Take(6),
                FeaturedTours = GetMockTours().Take(3),
                FeaturedHotels = GetMockHotels().Take(3),
                SearchForm = new SearchFormViewModel
                {
                    AvailableDestinations = GetMockDestinations()
                }
            };

            return View(viewModel);
        }

        public IActionResult Contact()
        {
            return View();
        }

        public IActionResult About()
        {
            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        // Mock data methods
        private List<DestinationDto> GetMockDestinations()
        {
            return new List<DestinationDto>
            {
                new DestinationDto { Id = 1, Name = "İstanbul", Country = "Türkiye", City = "İstanbul", Price = 1299, Duration = 3, ImageUrl = "https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b", IsActive = true },
                new DestinationDto { Id = 2, Name = "Antalya", Country = "Türkiye", City = "Antalya", Price = 899, Duration = 5, ImageUrl = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4", IsActive = true },
                new DestinationDto { Id = 3, Name = "Kapadokya", Country = "Türkiye", City = "Nevşehir", Price = 1599, Duration = 4, ImageUrl = "https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e", IsActive = true },
                new DestinationDto { Id = 4, Name = "Paris", Country = "Fransa", City = "Paris", Price = 2499, Duration = 6, ImageUrl = "https://images.unsplash.com/photo-1502602898536-47ad22581b52", IsActive = true },
                new DestinationDto { Id = 5, Name = "Roma", Country = "İtalya", City = "Roma", Price = 1899, Duration = 5, ImageUrl = "https://images.unsplash.com/photo-1552832230-c0197dd311b5", IsActive = true },
                new DestinationDto { Id = 6, Name = "Londra", Country = "İngiltere", City = "Londra", Price = 2199, Duration = 4, ImageUrl = "https://images.unsplash.com/photo-1513635269975-59663e0ac1ad", IsActive = true }
            };
        }

        private List<TourDto> GetMockTours()
        {
            return new List<TourDto>
            {
                new TourDto { Id = 1, Name = "İstanbul Tarihi Tur", DestinationId = 1, DestinationName = "İstanbul", Price = 299, Duration = 1, ImageUrl = "https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b", IsActive = true },
                new TourDto { Id = 2, Name = "Kapadokya Balon Turu", DestinationId = 3, DestinationName = "Kapadokya", Price = 599, Duration = 2, ImageUrl = "https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e", IsActive = true },
                new TourDto { Id = 3, Name = "Antalya Doğa Turu", DestinationId = 2, DestinationName = "Antalya", Price = 399, Duration = 3, ImageUrl = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4", IsActive = true }
            };
        }

        private List<HotelDto> GetMockHotels()
        {
            return new List<HotelDto>
            {
                new HotelDto { Id = 1, Name = "Grand Hotel İstanbul", DestinationId = 1, DestinationName = "İstanbul", StarRating = 5, PricePerNight = 450, ImageUrl = "https://images.unsplash.com/photo-1566073771259-6a8506099945", IsActive = true },
                new HotelDto { Id = 2, Name = "Antalya Resort & Spa", DestinationId = 2, DestinationName = "Antalya", StarRating = 5, PricePerNight = 380, ImageUrl = "https://images.unsplash.com/photo-1520250497591-112f2f40a3f4", IsActive = true },
                new HotelDto { Id = 3, Name = "Kapadokya Cave Hotel", DestinationId = 3, DestinationName = "Kapadokya", StarRating = 4, PricePerNight = 320, ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96", IsActive = true }
            };
        }
    }
}
