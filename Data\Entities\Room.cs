using System.ComponentModel.DataAnnotations;

namespace TravelTripProject.Data.Entities
{
    public class Room
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string RoomNumber { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string RoomType { get; set; } = string.Empty; // Single, Double, Suite, etc.
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public int MaxOccupancy { get; set; }
        
        public decimal PricePerNight { get; set; }
        
        public int Size { get; set; } // m² cinsinden
        
        public bool HasBalcony { get; set; }
        
        public bool HasSeaView { get; set; }
        
        public bool HasAirConditioning { get; set; }
        
        public bool HasMinibar { get; set; }
        
        public bool HasTv { get; set; }
        
        public bool IsAvailable { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedDate { get; set; }
        
        // Foreign Keys
        public int HotelId { get; set; }
        
        // Navigation Properties
        public virtual Hotel Hotel { get; set; } = null!;
        public virtual ICollection<Booking> Bookings { get; set; } = new List<Booking>();
    }
}
