using Microsoft.AspNetCore.Mvc;
using TravelTripProject.Models.ViewModels;
using TravelTripProject.Models.DTOs;

namespace TravelTripProject.Controllers
{
    public class ToursController : Controller
    {
        // private readonly ITourService _tourService;
        // private readonly IDestinationService _destinationService;

        // public ToursController(ITourService tourService, IDestinationService destinationService)
        // {
        //     _tourService = tourService;
        //     _destinationService = destinationService;
        // }

        public async Task<IActionResult> Index(int page = 1, int? destinationId = null, decimal? minPrice = null, decimal? maxPrice = null, DateTime? startDate = null)
        {
            // Geçici olarak mock data kullanıyoruz
            var tours = GetMockTours();
            
            // Filtreleme
            if (destinationId.HasValue)
                tours = tours.Where(t => t.DestinationId == destinationId.Value).ToList();
            
            if (minPrice.HasValue)
                tours = tours.Where(t => t.Price >= minPrice.Value).ToList();
                
            if (maxPrice.HasValue)
                tours = tours.Where(t => t.Price <= maxPrice.Value).ToList();
                
            if (startDate.HasValue)
                tours = tours.Where(t => t.StartDate >= startDate.Value).ToList();

            var viewModel = new TourListViewModel
            {
                Tours = new PagedResult<TourDto>
                {
                    Items = tours,
                    TotalCount = tours.Count,
                    PageNumber = page,
                    PageSize = 12
                },
                Filter = new TourFilterDto
                {
                    DestinationId = destinationId,
                    MinPrice = minPrice,
                    MaxPrice = maxPrice,
                    StartDate = startDate
                },
                Destinations = GetMockDestinations()
            };

            return View(viewModel);
        }

        public async Task<IActionResult> Details(int id)
        {
            // Geçici olarak mock data kullanıyoruz
            var tour = GetMockTours().FirstOrDefault(t => t.Id == id);
            if (tour == null)
            {
                return NotFound();
            }

            var destination = GetMockDestinations().FirstOrDefault(d => d.Id == tour.DestinationId);

            var viewModel = new TourDetailsViewModel
            {
                Tour = tour,
                Destination = destination,
                Itinerary = GetMockItinerary(id),
                RelatedTours = GetMockTours().Where(t => t.Id != id && t.DestinationId == tour.DestinationId).Take(3).ToList()
            };

            return View(viewModel);
        }

        // Mock data methods
        private List<TourDto> GetMockTours()
        {
            return new List<TourDto>
            {
                new TourDto 
                { 
                    Id = 1, 
                    Name = "İstanbul Tarihi Tur", 
                    Description = "İstanbul'un tarihi yerlerini keşfedin",
                    DestinationId = 1, 
                    DestinationName = "İstanbul",
                    DestinationCountry = "Türkiye",
                    Price = 299, 
                    Duration = 1, 
                    MaxCapacity = 20, 
                    AvailableSpots = 15, 
                    StartDate = DateTime.Now.AddDays(7), 
                    EndDate = DateTime.Now.AddDays(8),
                    ImageUrl = "https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b",
                    IsActive = true
                },
                new TourDto 
                { 
                    Id = 2, 
                    Name = "Kapadokya Balon Turu", 
                    Description = "Sıcak hava balonu ile Kapadokya manzarası",
                    DestinationId = 3, 
                    DestinationName = "Kapadokya",
                    DestinationCountry = "Türkiye",
                    Price = 599, 
                    Duration = 2, 
                    MaxCapacity = 15, 
                    AvailableSpots = 10, 
                    StartDate = DateTime.Now.AddDays(14), 
                    EndDate = DateTime.Now.AddDays(16),
                    ImageUrl = "https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e",
                    IsActive = true
                },
                new TourDto 
                { 
                    Id = 3, 
                    Name = "Antalya Doğa Turu", 
                    Description = "Antalya'nın doğal güzelliklerini keşfedin",
                    DestinationId = 2, 
                    DestinationName = "Antalya",
                    DestinationCountry = "Türkiye",
                    Price = 399, 
                    Duration = 3, 
                    MaxCapacity = 25, 
                    AvailableSpots = 20, 
                    StartDate = DateTime.Now.AddDays(21), 
                    EndDate = DateTime.Now.AddDays(24),
                    ImageUrl = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
                    IsActive = true
                }
            };
        }

        private List<DestinationDto> GetMockDestinations()
        {
            return new List<DestinationDto>
            {
                new DestinationDto { Id = 1, Name = "İstanbul", Country = "Türkiye" },
                new DestinationDto { Id = 2, Name = "Antalya", Country = "Türkiye" },
                new DestinationDto { Id = 3, Name = "Kapadokya", Country = "Türkiye" }
            };
        }

        private List<TourItineraryDto> GetMockItinerary(int tourId)
        {
            return new List<TourItineraryDto>
            {
                new TourItineraryDto { Day = 1, Title = "Varış ve Şehir Turu", Description = "Havalimanından karşılama ve şehir merkezine transfer", StartTime = TimeSpan.FromHours(9), EndTime = TimeSpan.FromHours(17) },
                new TourItineraryDto { Day = 2, Title = "Müze ve Tarihi Yerler", Description = "Ayasofya, Topkapı Sarayı ve Kapalıçarşı ziyareti", StartTime = TimeSpan.FromHours(10), EndTime = TimeSpan.FromHours(18) }
            };
        }
    }

    // Eksik ViewModel ve DTO'lar
    public class TourListViewModel
    {
        public PagedResult<TourDto> Tours { get; set; } = new();
        public TourFilterDto Filter { get; set; } = new();
        public IEnumerable<DestinationDto> Destinations { get; set; } = new List<DestinationDto>();
    }

    public class TourDetailsViewModel
    {
        public TourDto Tour { get; set; } = new();
        public DestinationDto? Destination { get; set; }
        public IEnumerable<TourItineraryDto> Itinerary { get; set; } = new List<TourItineraryDto>();
        public IEnumerable<TourDto> RelatedTours { get; set; } = new List<TourDto>();
    }

    public class TourItineraryDto
    {
        public int Day { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
    }
}
