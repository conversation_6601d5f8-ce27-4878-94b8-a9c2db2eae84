namespace TravelTripProject.Models.DTOs
{
    public class RoomDto
    {
        public int Id { get; set; }
        public string RoomNumber { get; set; } = string.Empty;
        public string RoomType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int MaxOccupancy { get; set; }
        public decimal PricePerNight { get; set; }
        public int Size { get; set; }
        public bool HasBalcony { get; set; }
        public bool HasSeaView { get; set; }
        public bool HasAirConditioning { get; set; }
        public bool HasMinibar { get; set; }
        public bool HasTv { get; set; }
        public bool IsAvailable { get; set; }
        public int HotelId { get; set; }
        public string HotelName { get; set; } = string.Empty;
    }

    public class CreateRoomDto
    {
        public string RoomNumber { get; set; } = string.Empty;
        public string RoomType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int MaxOccupancy { get; set; }
        public decimal PricePerNight { get; set; }
        public int Size { get; set; }
        public bool HasBalcony { get; set; }
        public bool HasSeaView { get; set; }
        public bool HasAirConditioning { get; set; }
        public bool HasMinibar { get; set; }
        public bool HasTv { get; set; }
        public int HotelId { get; set; }
    }

    public class UpdateRoomDto
    {
        public string RoomNumber { get; set; } = string.Empty;
        public string RoomType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int MaxOccupancy { get; set; }
        public decimal PricePerNight { get; set; }
        public int Size { get; set; }
        public bool HasBalcony { get; set; }
        public bool HasSeaView { get; set; }
        public bool HasAirConditioning { get; set; }
        public bool HasMinibar { get; set; }
        public bool HasTv { get; set; }
        public bool IsAvailable { get; set; }
        public int HotelId { get; set; }
    }
}
