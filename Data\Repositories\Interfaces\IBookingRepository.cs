using TravelTripProject.Data.Entities;

namespace TravelTripProject.Data.Repositories.Interfaces
{
    public interface IBookingRepository : IGenericRepository<Booking>
    {
        Task<IEnumerable<Booking>> GetBookingsByUserAsync(int userId);
        Task<IEnumerable<Booking>> GetBookingsByStatusAsync(string status);
        Task<Booking?> GetBookingByNumberAsync(string bookingNumber);
        Task<Booking?> GetBookingWithDetailsAsync(int id);
        Task<IEnumerable<Booking>> GetBookingsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Booking>> GetPendingBookingsAsync();
        Task<IEnumerable<Booking>> GetRecentBookingsAsync(int count = 10);
        Task<string> GenerateBookingNumberAsync();
        Task<bool> UpdateBookingStatusAsync(int bookingId, string status);
    }
}
