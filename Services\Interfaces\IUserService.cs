using TravelTripProject.Models.DTOs;
using TravelTripProject.Models.ViewModels;

namespace TravelTripProject.Services.Interfaces
{
    public interface IUserService
    {
        Task<IEnumerable<UserDto>> GetAllUsersAsync();
        Task<UserDto?> GetUserByIdAsync(int id);
        Task<UserDto?> GetUserByEmailAsync(string email);
        Task<UserProfileViewModel?> GetUserProfileAsync(int id);
        Task<IEnumerable<UserDto>> GetActiveUsersAsync();
        Task<PagedResult<UserDto>> GetPagedUsersAsync(int pageNumber, int pageSize, string? searchTerm = null);
        Task<UserDto> CreateUserAsync(CreateUserDto createDto);
        Task<UserDto?> UpdateUserAsync(int id, UpdateUserDto updateDto);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> ActivateUserAsync(int id);
        Task<bool> DeactivateUserAsync(int id);
        Task<bool> ConfirmEmailAsync(int id);
        Task<bool> UpdatePasswordAsync(int id, string currentPassword, string newPassword);
        Task<bool> ResetPasswordAsync(string email);
        Task<bool> EmailExistsAsync(string email);
        Task<UserDto?> AuthenticateAsync(string email, string password);
        Task<bool> UpdateLastLoginAsync(int id);
    }
}
