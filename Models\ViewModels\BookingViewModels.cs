using TravelTripProject.Models.DTOs;

namespace TravelTripProject.Models.ViewModels
{
    public class BookingDetailsViewModel
    {
        public BookingDto Booking { get; set; } = new();
        public UserDto User { get; set; } = new();
        public DestinationDto? Destination { get; set; }
        public TourDto? Tour { get; set; }
        public HotelDto? Hotel { get; set; }
        public IEnumerable<PaymentDto> Payments { get; set; } = new List<PaymentDto>();
    }

    public class CreateBookingViewModel
    {
        public CreateBookingDto Booking { get; set; } = new();
        public DestinationDto? Destination { get; set; }
        public TourDto? Tour { get; set; }
        public HotelDto? Hotel { get; set; }
        public IEnumerable<RoomDto> AvailableRooms { get; set; } = new List<RoomDto>();
        public decimal EstimatedTotal { get; set; }
    }

    public class BookingListViewModel
    {
        public PagedResult<BookingDto> Bookings { get; set; } = new();
        public BookingFilterDto Filter { get; set; } = new();
        public IEnumerable<string> StatusOptions { get; set; } = new List<string>();
        public IEnumerable<string> PaymentStatusOptions { get; set; } = new List<string>();
    }

    public class UserBookingsViewModel
    {
        public UserDto User { get; set; } = new();
        public IEnumerable<BookingDto> ActiveBookings { get; set; } = new List<BookingDto>();
        public IEnumerable<BookingDto> CompletedBookings { get; set; } = new List<BookingDto>();
        public IEnumerable<BookingDto> CancelledBookings { get; set; } = new List<BookingDto>();
    }
}
