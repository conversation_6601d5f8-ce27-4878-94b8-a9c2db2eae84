namespace TravelTripProject.Data.Repositories.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        IUserRepository Users { get; }
        IDestinationRepository Destinations { get; }
        ITourRepository Tours { get; }
        IHotelRepository Hotels { get; }
        IBookingRepository Bookings { get; }
        IGenericRepository<T> Repository<T>() where T : class;
        
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
