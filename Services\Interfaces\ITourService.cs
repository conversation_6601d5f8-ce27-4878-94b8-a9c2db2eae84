using TravelTripProject.Models.DTOs;
using TravelTripProject.Models.ViewModels;

namespace TravelTripProject.Services.Interfaces
{
    public interface ITourService
    {
        Task<IEnumerable<TourDto>> GetAllToursAsync();
        Task<TourDto?> GetTourByIdAsync(int id);
        Task<TourDetailsViewModel?> GetTourDetailsAsync(int id);
        Task<IEnumerable<TourDto>> GetToursByDestinationAsync(int destinationId);
        Task<IEnumerable<TourDto>> GetAvailableToursAsync();
        Task<IEnumerable<TourDto>> GetUpcomingToursAsync();
        Task<IEnumerable<TourDto>> GetToursByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<TourDto>> GetToursByPriceRangeAsync(decimal minPrice, decimal maxPrice);
        Task<PagedResult<TourDto>> GetPagedToursAsync(int pageNumber, int pageSize, TourFilterDto? filter = null);
        Task<TourDto> CreateTourAsync(CreateTourDto createDto);
        Task<TourDto?> UpdateTourAsync(int id, UpdateTourDto updateDto);
        Task<bool> DeleteTourAsync(int id);
        Task<bool> UpdateAvailableSpotsAsync(int tourId, int spotsToReduce);
        Task<bool> ActivateTourAsync(int id);
        Task<bool> DeactivateTourAsync(int id);
    }
}
