namespace TravelTripProject.Models.DTOs
{
    public class HotelDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int StarRating { get; set; }
        public decimal PricePerNight { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool HasWifi { get; set; }
        public bool HasPool { get; set; }
        public bool HasSpa { get; set; }
        public bool HasGym { get; set; }
        public bool IsActive { get; set; }
        public int DestinationId { get; set; }
        public string DestinationName { get; set; } = string.Empty;
        public int AvailableRooms { get; set; }
    }

    public class CreateHotelDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int StarRating { get; set; }
        public decimal PricePerNight { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool HasWifi { get; set; }
        public bool HasPool { get; set; }
        public bool HasSpa { get; set; }
        public bool HasGym { get; set; }
        public int DestinationId { get; set; }
    }

    public class UpdateHotelDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int StarRating { get; set; }
        public decimal PricePerNight { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool HasWifi { get; set; }
        public bool HasPool { get; set; }
        public bool HasSpa { get; set; }
        public bool HasGym { get; set; }
        public bool IsActive { get; set; }
        public int DestinationId { get; set; }
    }
}
